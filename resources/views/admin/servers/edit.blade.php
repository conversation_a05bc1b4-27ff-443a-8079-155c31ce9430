@extends('layouts.app')

@section('title', 'Edit SMS Server')

@section('css')
<style>
.required::after {
    content: " *";
    color: #f1416c;
    font-weight: bold;
}

.form-control.is-invalid {
    border-color: #f1416c;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23f1416c'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath d='m5.8 4.6 1.4 1.4M7.2 4.6l-1.4 1.4'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.form-control.is-valid {
    border-color: #50cd89;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%2350cd89' d='m2.3 6.73.94-.94 1.44 1.44L7.4 4.5l-.94-.94L4.07 6.06z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.auth-field-group {
    transition: all 0.3s ease;
}

.auth-field-group.hidden {
    display: none !important;
}

/* Authentication method selection styling */
.auth-method-selected {
    background-color: #f0f9ff;
    border: 2px solid #3b82f6;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.auth-method-selected .form-label {
    color: #1e40af;
    font-weight: 600;
}

#auth_fields_container .fv-row {
    transition: all 0.3s ease;
}

#auth_fields_container .row {
    transition: all 0.3s ease;
}

/* Ensure consistent styling for username/password row */
#username_password_row {
    margin-left: 0;
    margin-right: 0;
}

#username_password_row .col-md-6 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

/* Remove any unwanted borders from authentication fields */
#auth_fields_container .fv-row:not(.auth-method-selected) {
    border: none;
    background: transparent;
    padding: 0;
}

/* Ensure form controls have consistent styling */
#auth_fields_container .form-control {
    border: 1px solid #e4e6ef;
    background-color: #f8f9fa;
}

#auth_fields_container .form-control:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
}

/* Specific styling for username/password row to prevent unwanted borders */
#username_password_row:not(.auth-method-selected) {
    border: none !important;
    background: transparent !important;
    padding: 0 !important;
    margin-bottom: 0 !important;
}

/* Ensure individual field containers don't have unwanted styling */
#username_field:not(.auth-method-selected),
#password_field:not(.auth-method-selected) {
    border: none !important;
    background: transparent !important;
    padding: 0 !important;
}

/* Override any Bootstrap row margins that might cause issues */
#auth_fields_container .row {
    --bs-gutter-x: 1.5rem;
    margin-left: calc(-0.5 * var(--bs-gutter-x));
    margin-right: calc(-0.5 * var(--bs-gutter-x));
}

/* Ensure consistent spacing for form rows */
#auth_fields_container .fv-row {
    margin-bottom: 1.75rem;
}

/* Make sure auth-method-selected styling is applied correctly */
.auth-method-selected {
    background-color: #f0f9ff !important;
    border: 2px solid #3b82f6 !important;
    border-radius: 0.5rem !important;
    padding: 1rem !important;
    margin-bottom: 1rem !important;
}

/* Reset any default styling that might interfere */
#auth_fields_container {
    background: transparent;
    border: none;
    padding: 0;
}

/* Ensure clean slate for authentication fields */
#api_key_field,
#username_password_row {
    background: transparent;
    border: none;
    padding: 0;
    margin-bottom: 1.75rem;
    transition: all 0.3s ease;
}

/* Only apply special styling when explicitly selected */
#api_key_field.auth-method-selected,
#username_password_row.auth-method-selected {
    background-color: #f0f9ff;
    border: 2px solid #3b82f6;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

/* Parameter styling */
.parameter-row {
    border: 1px solid #e4e6ef;
    border-radius: 0.475rem;
    padding: 1rem;
    margin-bottom: 1rem;
    background-color: #f8f9fa;
}

.parameter-row:hover {
    background-color: #f1f3f8;
    border-color: #d1d3e0;
}

#json_preview {
    background-color: #2d3748!important;
    color: #e2e8f0;
    font-family: 'Courier New', monospace;
    font-size: 0.875rem;
    font-weight: 500;
    line-height: 1.6;
    border-radius: 0.475rem;
    padding: 1.25rem;
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #4a5568;
}

.dropdown-menu {
    max-height: 200px;
    overflow-y: auto;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
    cursor: pointer;
}
</style>
@endsection

@section('content')
    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">
        <!--begin::Server Edit-->
        <div class="row g-7">
            <!--begin::Content-->
            <div class="col-lg-6 col-xl-9">
                <!--begin::Contacts-->
                <div class="card card-flush h-lg-100" id="kt_contacts_main">
                    <!--begin::Card header-->
                    <div class="card-header pt-7" id="kt_chat_contacts_header">
                        <!--begin::Card title-->
                        <div class="card-title">
                            <!--begin::Svg Icon | path: icons/duotune/communication/com005.svg-->
                            <span class="svg-icon svg-icon-1 me-2">
                                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M20 14H18V10H20C20.6 10 21 10.4 21 11V13C21 13.6 20.6 14 20 14ZM21 19V17C21 16.4 20.6 16 20 16H18V20H20C20.6 20 21 19.6 21 19ZM21 7V5C21 4.4 20.6 4 20 4H18V8H20C20.6 8 21 7.6 21 7Z" fill="currentColor" />
                                    <path opacity="0.3" d="M17 22H3C2.4 22 2 21.6 2 21V3C2 2.4 2.4 2 3 2H17C17.6 2 18 2.4 18 3V21C18 21.6 17.6 22 17 22ZM10 7C8.9 7 8 7.9 8 9C8 10.1 8.9 11 10 11C11.1 11 12 10.1 12 9C12 7.9 11.1 7 10 7ZM13.3 16C14 16 14.5 15.3 14.3 14.7C13.7 13.2 12 12 10.1 12C8.10001 12 6.49999 13.1 5.89999 14.7C5.59999 15.3 6.19999 16 7.39999 16H13.3Z" fill="currentColor" />
                                </svg>
                            </span>
                            <!--end::Svg Icon-->
                            <h2>Edit SMS Server</h2>
                        </div>
                        <!--end::Card title-->
                    </div>
                    <!--end::Card header-->
                    <!--begin::Card body-->
                    <div class="card-body pt-5">
                        <!--begin::Form-->
                        <form method="POST" class="form" action="{{ route('admin.servers.update', $server->id) }}">
                            @csrf
                            @method('PATCH')
                            <!--begin::Input group-->
                            <div class="fv-row mb-7">
                                <!--begin::Label-->
                                <label class="fs-6 fw-semibold form-label mt-3">
                                    <span class="required">Gateway Provider Type</span>
                                    <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Select the SMS gateway provider type."></i>
                                </label>
                                <!--end::Label-->
                                <!--begin::Select2-->
                                <select class="form-select form-select-solid" name="gateway_type" id="gateway_type" required>
                                    <option value="">Select Gateway Provider</option>
                                    @foreach($gatewayProviderTypes as $providerType)
                                        <option value="{{ $providerType->name }}"
                                                data-auth-method="{{ $providerType->auth_method }}"
                                                data-http-method="{{ $providerType->http_method }}"
                                                data-description="{{ $providerType->description }}"
                                                {{ $server->gateway_type == $providerType->name ? 'selected' : '' }}>
                                            {{ $providerType->display_name }}
                                        </option>
                                    @endforeach
                                </select>
                                <!--end::Select2-->
                                <div class="form-text" id="gateway_description"></div>
                                @error('gateway_type')
                                    <div class="fv-plugins-message-container invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <!--end::Input group-->

                            <!--begin::Input group-->
                            <div class="fv-row mb-7">
                                <!--begin::Label-->
                                <label class="fs-6 fw-semibold form-label mt-3">
                                    <span class="required">Server Name</span>
                                    <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Enter a unique name for this server configuration."></i>
                                </label>
                                <!--end::Label-->
                                <!--begin::Input-->
                                <input type="text" class="form-control form-control-solid" name="name" value="{{ old('name', $server->name) }}" placeholder="e.g., My RouteMobile Server" required />
                                <!--end::Input-->
                                @error('name')
                                    <div class="fv-plugins-message-container invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <!--end::Input group-->
                            <!--begin::Input group-->
                            <div class="fv-row mb-7">
                                <!--begin::Label-->
                                <label class="fs-6 fw-semibold form-label mt-3">
                                    <span class="required">API Endpoint URL</span>
                                    <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Enter the complete API endpoint URL."></i>
                                </label>
                                <!--end::Label-->
                                <!--begin::Input-->
                                <input type="url" class="form-control form-control-solid" name="api_link" value="{{ old('api_link', $server->api_link) }}" placeholder="https://api.provider.com/sms/send" required />
                                <!--end::Input-->
                                @error('api_link')
                                    <div class="fv-plugins-message-container invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>


                            <!--begin::Authentication Section-->
                            <div class="alert alert-info d-flex align-items-center p-5 mb-7" id="auth_info">
                                <span class="svg-icon svg-icon-2hx svg-icon-info me-4">
                                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path opacity="0.3" d="M2 4V16C2 16.6 2.4 17 3 17H13L16.6 20.6C17.1 21.1 18 20.8 18 20V17H21C21.6 17 22 16.6 22 16V4C22 3.4 21.6 3 21 3H3C2.4 3 2 3.4 2 4Z" fill="currentColor"/>
                                        <path d="M18 9H6C5.4 9 5 8.6 5 8C5 7.4 5.4 7 6 7H18C18.6 7 19 7.4 19 8C19 8.6 18.6 9 18 9ZM16 12C16 11.4 15.6 11 15 11H6C5.4 11 5 11.4 5 12C5 12.6 5.4 13 6 13H15C15.6 13 16 12.6 16 12Z" fill="currentColor"/>
                                    </svg>
                                </span>
                                <div class="d-flex flex-column">
                                    <span id="auth_method_info">Configure authentication credentials for your SMS provider.</span>
                                </div>
                            </div>
                            <!--end::Authentication Section-->

                            <!--begin::Authentication Fields-->
                            <div id="auth_fields_container">
                                <!--begin::API Key Field-->
                                <div class="fv-row mb-7" id="api_key_field">
                                    <label class="fs-6 fw-semibold form-label">
                                        <span id="api_key_label">API Key</span>
                                        <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Enter the API key for authentication."></i>
                                    </label>
                                    <input type="text" class="form-control form-control-solid" name="api_key" id="api_key" value="{{ old('api_key', $server->api_key) }}" placeholder="Enter your API key" />
                                    @error('api_key')
                                        <div class="fv-plugins-message-container invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <!--end::API Key Field-->

                                <!--begin::Username & Password Row-->
                                <div class="row" id="username_password_row">
                                    <!--begin::Username Field-->
                                    <div class="col-md-6" id="username_field">
                                        <div class="fv-row mb-7">
                                            <label class="fs-6 fw-semibold form-label">
                                                <span id="username_label">Username</span>
                                                <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Enter the username for authentication."></i>
                                            </label>
                                            <input type="text" class="form-control form-control-solid" name="username" id="username" value="{{ old('username', $server->username) }}" placeholder="Enter username" />
                                            @error('username')
                                                <div class="fv-plugins-message-container invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <!--end::Username Field-->

                                    <!--begin::Password Field-->
                                    <div class="col-md-6" id="password_field">
                                        <div class="fv-row mb-7">
                                            <label class="fs-6 fw-semibold form-label">
                                                <span id="password_label">Password</span>
                                                <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Enter the password for authentication."></i>
                                            </label>
                                            <input type="password" class="form-control form-control-solid" name="password" id="password" value="{{ old('password', $server->password) }}" placeholder="Enter password" />
                                            @error('password')
                                                <div class="fv-plugins-message-container invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                    <!--end::Password Field-->
                                </div>
                                <!--end::Username & Password Row-->
                            </div>
                            <!--end::Authentication Fields-->

                            <!--begin::API Parameters Section-->
                            <div class="fv-row mb-7" id="api_parameters_section" style="display: none;">
                                <!--begin::Label-->
                                <label class="fs-6 fw-semibold form-label mt-3">
                                    <span>API Configuration</span>
                                    <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Configure the authentication and parameters for your custom SMS provider"></i>
                                </label>
                                <!--end::Label-->

                                <!--begin::Parameters Info-->
                                <div class="alert alert-info d-flex align-items-center p-5 mb-5">
                                    <i class="ki-duotone ki-information-5 fs-2hx text-info me-4">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                        <span class="path3"></span>
                                    </i>
                                    <div class="d-flex flex-column">
                                        <h4 class="mb-1 text-info">Available Placeholders</h4>
                                        <span>Use these placeholders in your parameter values: <code>{api_key}</code>, <code>{username}</code>, <code>{password}</code>, <code>{to}</code>, <code>{sender}</code>, <code>{message}</code>, <code>{type}</code>, <code>{timestamp}</code></span>
                                    </div>
                                </div>
                                <!--end::Parameters Info-->

                                <!--begin::Parameters Container-->
                                <div id="api_parameters_container">
                                    <div class="row mb-3">
                                        <div class="col-5">
                                            <label class="form-label fw-bold">Parameter Name</label>
                                        </div>
                                        <div class="col-5">
                                            <label class="form-label fw-bold">Parameter Value</label>
                                        </div>
                                        <div class="col-2">
                                            <label class="form-label fw-bold">Action</label>
                                        </div>
                                    </div>

                                    <!--begin::Parameter Row Template-->
                                    <div class="parameter-row row mb-3" style="display: none;" id="parameter_row_template">
                                        <div class="col-5">
                                            <input type="text" class="form-control form-control-solid parameter-key" placeholder="e.g., api_key, to, message" />
                                        </div>
                                        <div class="col-5">
                                            <div class="input-group">
                                                <input type="text" class="form-control form-control-solid parameter-value" placeholder="e.g., {api_key}, {to}, {message}" />
                                                <button class="btn btn-light-primary btn-sm" type="button" onclick="showPlaceholderDropdown(this)">
                                                    <i class="fas fa-plus"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <div class="col-2">
                                            <button type="button" class="btn btn-light-danger btn-sm remove-parameter">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                    <!--end::Parameter Row Template-->
                                </div>
                                <!--end::Parameters Container-->

                                <!--begin::Add Parameter Button-->
                                <div class="d-flex justify-content-start mb-5">
                                    <button type="button" class="btn btn-light-primary btn-sm" id="add_parameter_btn">
                                        <i class="fas fa-plus"></i> Add Parameter
                                    </button>
                                </div>
                                <!--end::Add Parameter Button-->

                                <!--begin::Hidden Input for JSON-->
                                <input type="hidden" name="api_parameters" id="api_parameters_json" value="{{ json_encode($server->api_parameters ?? []) }}" />
                                <!--end::Hidden Input for JSON-->

                                <!--begin::Preview Section-->
                                <div class="card card-flush">
                                    <div class="card-header">
                                        <h3 class="card-title">JSON Preview</h3>
                                    </div>
                                    <div class="card-body">
                                        <pre id="json_preview" class="bg-light p-3 rounded">{{ json_encode($server->api_parameters ?? [], JSON_PRETTY_PRINT) }}</pre>
                                    </div>
                                </div>
                                <!--end::Preview Section-->
                            </div>
                            <!--end::API Parameters Section-->

                            <!--begin::Input group-->
                            <div class="fv-row mb-7">
                                <!--begin::Label-->
                                <label class="fs-6 fw-semibold form-label mt-3">
                                    <span>Status</span>
                                    <i class="fas fa-exclamation-circle ms-1 fs-7" data-bs-toggle="tooltip" title="Status"></i>
                                </label>
                                <!--end::Label-->
                                <!--begin::Row-->
                                <div class="row row-cols-2 row-cols-md-2 row-cols-lg-2 row-cols-xl-2 g-3"
                                     data-kt-buttons="true" data-kt-buttons-target="[data-kt-button='true']">
                                    <!--begin::Col-->
                                    <div class="col">
                                        <!--begin::Option-->
                                        <label class="btn btn-outline btn-outline-dashed btn-active-light-primary {{ old('status', $server->status) == 'enabled' ? 'active' : '' }} d-flex text-start p-6" data-kt-button="true">
                                            <!--begin::Radio-->
                                            <span class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                                <input class="form-check-input" type="radio" name="status" value="enabled" {{ old('status', $server->status) == 'enabled' ? 'checked' : '' }}/>
                                            </span>
                                            <!--end::Radio-->
                                            <!--begin::Info-->
                                            <span class="ms-5">
                                                <span class="fs-4 fw-bold text-gray-800 d-block">Enabled</span>
                                            </span>
                                            <!--end::Info-->
                                        </label>
                                        <!--end::Option-->
                                    </div>
                                    <!--end::Col-->
                                    <!--begin::Col-->
                                    <div class="col">
                                        <!--begin::Option-->
                                        <label class="btn btn-outline btn-outline-dashed btn-active-light-primary {{ old('status', $server->status) == 'disabled' ? 'active' : '' }} d-flex text-start p-6" data-kt-button="true">
                                            <!--begin::Radio-->
                                            <span class="form-check form-check-custom form-check-solid form-check-sm align-items-start mt-1">
                                                <input class="form-check-input" type="radio" name="status" value="disabled" {{ old('status', $server->status) == 'disabled' ? 'checked' : '' }}/>
                                            </span>
                                            <!--end::Radio-->
                                            <!--begin::Info-->
                                            <span class="ms-5">
                                                <span class="fs-4 fw-bold text-gray-800 d-block">Disabled</span>
                                            </span>
                                            <!--end::Info-->
                                        </label>
                                        <!--end::Option-->
                                    </div>
                                    <!--end::Col-->
                                </div>
                                <!--end::Row-->
                            </div>
                            <!--end::Input group-->
                            <!--begin::Separator-->
                            <div class="separator mb-6"></div>
                            <!--end::Separator-->
                            <!--begin::Action buttons-->
                            <div class="d-flex justify-content-end">
                                <!--begin::Button-->
                                <button type="reset" data-kt-contacts-type="cancel" class="btn btn-light me-3">Cancel</button>
                                <!--end::Button-->
                                <!--begin::Button-->
                                <button type="submit" data-kt-contacts-type="submit" class="btn btn-primary">
                                    <span class="indicator-label">Save</span>
                                    <span class="indicator-progress">Please wait...
                                                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span></span>
                                </button>
                                <!--end::Button-->
                            </div>
                            <!--end::Action buttons-->
                        </form>
                        <!--end::Form-->
                    </div>
                    <!--end::Card body-->
                </div>
                <!--end::Contacts-->
            </div>

            <!--end::Content-->
        </div>
        <!--end::Server Edit-->
    </div>
    <!--end::Container-->
@stop



@section('js')
<script>
$(document).ready(function() {

    // Handle gateway type selection
    $('#gateway_type').on('change', function() {
        const selectedOption = $(this).find('option:selected');
        const authMethod = selectedOption.data('auth-method');
        const description = selectedOption.data('description');
        const selectedValue = $(this).val();

        // Update description
        $('#gateway_description').text(description || '');

        // Show/hide API parameters section for custom gateway
        if (selectedValue === 'custom') {
            $('#api_parameters_section').show();
            loadExistingParameters();
            // For custom providers, always show all authentication fields
            showAllAuthFields();
            updateAuthInfo('Please provide authentication credentials: API Key, Username & Password, or both.');
        } else {
            $('#api_parameters_section').hide();
            // For standard providers, use the auth method to determine fields
            toggleAuthFields(authMethod);
        }

        // Update authentication field visibility based on provider type
        updateAuthFieldsVisibility();

        // Load gateway configuration if not custom
        if ($(this).val() && $(this).val() !== 'custom') {
            loadGatewayConfig($(this).val());
        }
    });

    // Handle authentication field changes for validation and dynamic behavior
    $('#api_key, #username, #password').on('input', function() {
        validateAuthFields();
        updateAuthFieldsVisibility();

        // Reload default parameters for custom providers when auth fields change
        const gatewayType = $('#gateway_type').val();
        if (gatewayType === 'custom') {
            loadExistingParameters();
        }
    });

    // Update authentication fields visibility and styling based on input
    function updateAuthFieldsVisibility() {
        const gatewayType = $('#gateway_type').val();
        const apiKey = $('#api_key').val().trim();
        const username = $('#username').val().trim();
        const password = $('#password').val().trim();

        // For custom providers, always show all authentication fields
        if (gatewayType === 'custom') {
            // Ensure all fields are visible for custom providers
            $('#api_key_field').show();
            $('#username_field').show();
            $('#password_field').show();
            $('#username_password_row').show();

            // Update field styling based on what's filled
            if (apiKey) {
                $('#api_key_field').addClass('auth-method-selected');
            } else {
                $('#api_key_field').removeClass('auth-method-selected');
            }

            if (username || password) {
                $('#username_password_row').addClass('auth-method-selected');
            } else {
                $('#username_password_row').removeClass('auth-method-selected');
            }

            // Update info message for custom providers
            if (apiKey && (username && password)) {
                updateAuthInfo('Using both API Key and Username & Password authentication.');
            } else if (apiKey) {
                updateAuthInfo('Using API Key authentication. You can also add Username & Password if needed.');
            } else if (username && password) {
                updateAuthInfo('Using Username & Password authentication. You can also add an API Key if needed.');
            } else {
                updateAuthInfo('Please provide authentication credentials: API Key, Username & Password, or both.');
            }
        } else {
            // For other providers, use the original logic
            if (apiKey) {
                $('#api_key_field').addClass('auth-method-selected');
                $('#username_password_row').removeClass('auth-method-selected');
            } else if (username || password) {
                $('#username_password_row').addClass('auth-method-selected');
                $('#api_key_field').removeClass('auth-method-selected');
            } else {
                $('#api_key_field, #username_password_row').removeClass('auth-method-selected');
            }

            // Update info message for standard providers
            if (apiKey && (username && password)) {
                updateAuthInfo('Using both API Key and Username & Password authentication.');
            } else if (apiKey) {
                updateAuthInfo('Using API Key authentication.');
            } else if (username && password) {
                updateAuthInfo('Using Username & Password authentication.');
            } else {
                updateAuthInfo('Please provide either an API Key OR Username & Password for authentication.');
            }
        }
    }

    // Initialize API Parameters functionality
    initializeApiParameters();

    // Reset authentication field styling to ensure clean state
    resetAuthenticationFieldStyling();

    // Initialize authentication field behavior
    updateAuthFieldsVisibility();

    // Initialize form based on current selection
    if ($('#gateway_type').val()) {
        $('#gateway_type').trigger('change');
    }

    // Form submission validation
    $('form').on('submit', function(e) {
        if (!validateAuthFields()) {
            e.preventDefault();
            return false;
        }
    });

    function toggleAuthFields(authMethod) {
        // Reset all fields visibility and requirements
        resetAuthFields();

        // Show relevant fields based on auth method
        switch(authMethod) {
            case 'api_key':
                showApiKeyOnly();
                updateAuthInfo('This provider requires an API Key for authentication.');
                break;
            case 'username_password':
            case 'basic_auth':
                showUsernamePasswordOnly();
                updateAuthInfo('This provider requires Username & Password for authentication.');
                break;
            case 'bearer_token':
                showBearerTokenOnly();
                updateAuthInfo('This provider requires a Bearer Token for authentication.');
                break;
            default:
                showAllAuthFields();
                updateAuthInfo('Please provide either an API Key OR Username & Password for authentication.');
        }

        // Validate after toggling
        validateAuthFields();
    }

    function resetAuthFields() {
        // Show all fields initially
        $('#api_key_field, #username_field, #password_field').show();

        // Remove required indicators
        $('#api_key_label, #username_label, #password_label').removeClass('required');
        $('#api_key_label span, #username_label span, #password_label span').removeClass('required');

        // Reset labels
        $('#api_key_label span').text('API Key');
        $('#username_label span').text('Username');
        $('#password_label span').text('Password');

        // Clear validation states
        $('#api_key, #username, #password').removeClass('is-invalid is-valid');
    }

    function showApiKeyOnly() {
        $('#api_key_field').show();
        $('#username_field, #password_field').hide();
        $('#api_key_label span').addClass('required').text('API Key');
    }

    function showUsernamePasswordOnly() {
        $('#username_field, #password_field').show();
        $('#api_key_field').hide();
        $('#username_label span, #password_label span').addClass('required');
    }

    function showBearerTokenOnly() {
        $('#api_key_field').show();
        $('#username_field, #password_field').hide();
        $('#api_key_label span').addClass('required').text('Bearer Token');
    }

    function showAllAuthFields() {
        $('#api_key_field, #username_field, #password_field').show();
        // No fields are individually required when all are shown
    }

    function updateAuthInfo(message) {
        $('#auth_method_info').text(message);
    }

    function validateAuthFields() {
        const apiKey = $('#api_key').val().trim();
        const username = $('#username').val().trim();
        const password = $('#password').val().trim();

        // Clear previous validation states
        $('#api_key, #username, #password').removeClass('is-invalid is-valid');

        let isValid = false;

        // Check if we have either API key OR username+password
        if (apiKey) {
            isValid = true;
            $('#api_key').addClass('is-valid');
        } else if (username && password) {
            isValid = true;
            $('#username, #password').addClass('is-valid');
        } else {
            // Show validation errors
            if (!apiKey && !username && !password) {
                $('#api_key').addClass('is-invalid');
                showValidationError('Please provide either API Key or Username & Password');
            } else if (username && !password) {
                $('#password').addClass('is-invalid');
                showValidationError('Password is required when Username is provided');
            } else if (!username && password) {
                $('#username').addClass('is-invalid');
                showValidationError('Username is required when Password is provided');
            }
        }

        return isValid;
    }

    function showValidationError(message) {
        // You can implement a toast notification or alert here
    }

    function loadGatewayConfig(gatewayType) {
        $.get('{{ route("admin.servers.gateway-config") }}', {
            gateway_type: gatewayType
        })
        .done(function(data) {
            if (data.provider_type) {
                // You can populate additional fields here if needed
                console.log('Gateway config loaded:', data);
            }
        })
        .fail(function(xhr) {
            console.error('Failed to load gateway config:', xhr.responseJSON);
        });
    }

    // API Parameters functionality
    function initializeApiParameters() {
        // Add parameter button
        $('#add_parameter_btn').on('click', function() {
            addParameterRow();
        });

        // Handle parameter changes
        $(document).on('input', '.parameter-key, .parameter-value', function() {
            updateJsonPreview();
        });

        // Handle remove parameter
        $(document).on('click', '.remove-parameter', function() {
            $(this).closest('.parameter-row').remove();
            updateJsonPreview();
        });

        // Initialize with existing parameters if custom is selected
        const currentGatewayType = $('#gateway_type').val();
        if (currentGatewayType === 'custom') {
            $('#api_parameters_section').show();
            loadExistingParameters();
        }
    }

    function addParameterRow(key = '', value = '') {
        const template = $('#parameter_row_template').clone();
        template.attr('id', '').show();
        template.find('.parameter-key').val(key);
        template.find('.parameter-value').val(value);

        $('#api_parameters_container').append(template);
        updateJsonPreview();
    }

    function loadExistingParameters() {
        // Clear existing parameters
        $('.parameter-row:not(#parameter_row_template)').remove();

        // Load existing parameters from server data
        const existingParams = @json($server->api_parameters ?? []);

        if (Object.keys(existingParams).length > 0) {
            Object.entries(existingParams).forEach(([key, value]) => {
                addParameterRow(key, value);
            });
        } else {
            // For custom providers, add authentication parameters based on what's available
            let defaultParams = [];

            // Check which authentication fields have values and add corresponding parameters
            const apiKey = $('#api_key').val().trim();
            const username = $('#username').val().trim();
            const password = $('#password').val().trim();

            // Add authentication parameters
            if (apiKey) {
                defaultParams.push({ key: 'api_key', value: '{api_key}' });
            }

            if (username && password) {
                defaultParams.push({ key: 'username', value: '{username}' });
                defaultParams.push({ key: 'password', value: '{password}' });
            }

            // Add common SMS parameters
            defaultParams.push(
                { key: 'to', value: '{to}' },
                { key: 'message', value: '{message}' },
                { key: 'from', value: '{sender}' }
            );

            defaultParams.forEach(param => {
                addParameterRow(param.key, param.value);
            });
        }
    }

    function updateJsonPreview() {
        const parameters = {};
        $('.parameter-row:not(#parameter_row_template)').each(function() {
            const key = $(this).find('.parameter-key').val().trim();
            const value = $(this).find('.parameter-value').val().trim();

            if (key && value) {
                parameters[key] = value;
            }
        });

        const jsonString = JSON.stringify(parameters, null, 2);
        $('#json_preview').text(jsonString);
        $('#api_parameters_json').val(JSON.stringify(parameters));
    }

    function showPlaceholderDropdown(button) {
        const placeholders = [
            '{api_key}', '{username}', '{password}', '{bearer_token}',
            '{to}', '{sender}', '{message}', '{type}', '{type_mapped}',
            '{timestamp}', '{batch_id}', '{company_id}'
        ];

        // Create dropdown menu
        let dropdown = '<div class="dropdown-menu show" style="position: absolute; z-index: 1000;">';
        placeholders.forEach(placeholder => {
            dropdown += `<a class="dropdown-item" href="#" onclick="insertPlaceholder('${placeholder}', this)">${placeholder}</a>`;
        });
        dropdown += '</div>';

        // Remove existing dropdown
        $('.dropdown-menu').remove();

        // Add dropdown
        $(button).parent().append(dropdown);

        // Position dropdown
        const $dropdown = $(button).parent().find('.dropdown-menu');
        $dropdown.css({
            'top': $(button).outerHeight() + 'px',
            'left': '0px'
        });

        // Close dropdown when clicking outside
        $(document).on('click.placeholder-dropdown', function(e) {
            if (!$(e.target).closest('.input-group').length) {
                $('.dropdown-menu').remove();
                $(document).off('click.placeholder-dropdown');
            }
        });
    }

    function insertPlaceholder(placeholder, element) {
        const $inputGroup = $(element).closest('.input-group');
        const $input = $inputGroup.find('.parameter-value');
        const currentValue = $input.val();

        // Insert placeholder at cursor position or append
        $input.val(currentValue + placeholder);
        $input.focus();

        // Remove dropdown
        $('.dropdown-menu').remove();
        $(document).off('click.placeholder-dropdown');

        // Update preview
        updateJsonPreview();
    }
});
</script>
@endsection
